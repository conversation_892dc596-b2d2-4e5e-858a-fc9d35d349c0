<?php $__env->startSection('title', 'Shop - ' . __('common.company_name')); ?>
<?php $__env->startSection('meta_description', 'Browse our collection of digital products and services. Find the perfect solution for your business needs.'); ?>
<?php $__env->startSection('meta_keywords', 'shop, products, digital services, e-commerce, online store'); ?>

<?php $__env->startSection('og_title', 'Shop - ' . __('common.company_name')); ?>
<?php $__env->startSection('og_description', 'Browse our collection of digital products and services. Find the perfect solution for your business needs.'); ?>
<?php $__env->startSection('twitter_title', 'Shop - ' . __('common.company_name')); ?>
<?php $__env->startSection('twitter_description', 'Browse our collection of digital products and services. Find the perfect solution for your business needs.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <h1 class="heading-1 text-white mb-6">
                Our <span class="text-blue-300">Shop</span>
            </h1>
            <p class="text-lead text-blue-100 mb-8">
                Discover our range of digital products and services designed to help your business grow and succeed in the digital world.
            </p>
            
            <!-- Search Bar -->
            <div class="max-w-2xl mx-auto">
                <form action="<?php echo e(route('shop.index')); ?>" method="GET" class="relative">
                    <input type="text" name="search" value="<?php echo e(request('search')); ?>" 
                           placeholder="Search products..." 
                           class="w-full px-6 py-4 pr-16 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-300">
                    <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Shop Content -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Sidebar Filters -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm p-6 sticky top-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Filters</h3>
                    
                    <form action="<?php echo e(route('shop.index')); ?>" method="GET" id="filter-form">
                        <?php if(request('search')): ?>
                            <input type="hidden" name="search" value="<?php echo e(request('search')); ?>">
                        <?php endif; ?>
                        
                        <!-- Categories -->
                        <?php if($categories->count() > 0): ?>
                        <div class="mb-8">
                            <h4 class="font-medium text-gray-900 mb-4">Categories</h4>
                            <div class="space-y-2">
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div>
                                    <label class="flex items-center">
                                        <input type="radio" name="category" value="<?php echo e($category->slug); ?>" 
                                               <?php echo e(request('category') === $category->slug ? 'checked' : ''); ?>

                                               class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700"><?php echo e($category->name); ?></span>
                                        <span class="ml-auto text-xs text-gray-500">(<?php echo e($category->products_count); ?>)</span>
                                    </label>
                                    
                                    <?php if($category->children->count() > 0): ?>
                                    <div class="ml-6 mt-2 space-y-2">
                                        <?php $__currentLoopData = $category->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <label class="flex items-center">
                                            <input type="radio" name="category" value="<?php echo e($child->slug); ?>" 
                                                   <?php echo e(request('category') === $child->slug ? 'checked' : ''); ?>

                                                   class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-600"><?php echo e($child->name); ?></span>
                                            <span class="ml-auto text-xs text-gray-500">(<?php echo e($child->products_count); ?>)</span>
                                        </label>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                
                                <?php if(request('category')): ?>
                                <div class="pt-2">
                                    <button type="button" onclick="clearCategory()" class="text-sm text-blue-600 hover:text-blue-700">
                                        Clear Category Filter
                                    </button>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Price Range -->
                        <?php if($priceRange && $priceRange->min_price !== $priceRange->max_price): ?>
                        <div class="mb-8">
                            <h4 class="font-medium text-gray-900 mb-4">Price Range</h4>
                            <div class="space-y-4">
                                <div class="grid grid-cols-2 gap-2">
                                    <div>
                                        <label class="block text-xs text-gray-600 mb-1">Min Price</label>
                                        <input type="number" name="min_price" value="<?php echo e(request('min_price')); ?>" 
                                               min="<?php echo e($priceRange->min_price); ?>" max="<?php echo e($priceRange->max_price); ?>"
                                               placeholder="R<?php echo e(number_format($priceRange->min_price, 0)); ?>"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-600 mb-1">Max Price</label>
                                        <input type="number" name="max_price" value="<?php echo e(request('max_price')); ?>" 
                                               min="<?php echo e($priceRange->min_price); ?>" max="<?php echo e($priceRange->max_price); ?>"
                                               placeholder="R<?php echo e(number_format($priceRange->max_price, 0)); ?>"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                                    </div>
                                </div>
                                <div class="text-xs text-gray-500">
                                    Range: R<?php echo e(number_format($priceRange->min_price, 0)); ?> - R<?php echo e(number_format($priceRange->max_price, 0)); ?>

                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Filter Actions -->
                        <div class="space-y-3">
                            <button type="submit" class="w-full btn-primary">
                                Apply Filters
                            </button>
                            <a href="<?php echo e(route('shop.index')); ?>" class="w-full btn-outline text-center block">
                                Clear All Filters
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="lg:col-span-3">
                <!-- Sort and Results Info -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                    <div class="text-gray-600 mb-4 sm:mb-0">
                        Showing <?php echo e($products->firstItem() ?? 0); ?>-<?php echo e($products->lastItem() ?? 0); ?> of <?php echo e($products->total()); ?> products
                        <?php if(request('search')): ?>
                            for "<strong><?php echo e(request('search')); ?></strong>"
                        <?php endif; ?>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <label class="text-sm text-gray-600">Sort by:</label>
                        <select name="sort" onchange="updateSort(this)" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                            <option value="name" <?php echo e(request('sort') === 'name' ? 'selected' : ''); ?>>Name</option>
                            <option value="price" <?php echo e(request('sort') === 'price' ? 'selected' : ''); ?>>Price</option>
                            <option value="created_at" <?php echo e(request('sort') === 'created_at' ? 'selected' : ''); ?>>Newest</option>
                            <option value="featured" <?php echo e(request('sort') === 'featured' ? 'selected' : ''); ?>>Featured</option>
                        </select>
                    </div>
                </div>
                
                <?php if($products->count() > 0): ?>
                <!-- Products Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="product-card bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                        <div class="relative">
                            <a href="<?php echo e(route('shop.product', $product->slug)); ?>">
                                <img src="<?php echo e($product->primary_image); ?>" alt="<?php echo e($product->name); ?>" 
                                     class="w-full h-48 object-cover hover-lift">
                            </a>
                            
                            <?php if($product->discount_percentage > 0): ?>
                            <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                                -<?php echo e($product->discount_percentage); ?>%
                            </div>
                            <?php endif; ?>
                            
                            <?php if($product->is_featured): ?>
                            <div class="absolute top-2 right-2 bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium">
                                Featured
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="p-4">
                            <div class="mb-2">
                                <?php $__currentLoopData = $product->categories->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <span class="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded mr-1">
                                    <?php echo e($category->name); ?>

                                </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                <a href="<?php echo e(route('shop.product', $product->slug)); ?>" class="hover:text-blue-600 transition-colors">
                                    <?php echo e($product->name); ?>

                                </a>
                            </h3>
                            
                            <?php if($product->short_description): ?>
                            <div class="text-gray-600 text-sm mb-3 line-clamp-2 prose prose-sm max-w-none">
                                <?php echo $product->short_description; ?>

                            </div>
                            <?php endif; ?>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="text-lg font-bold text-gray-900"><?php echo e($product->formatted_price); ?></span>
                                    <?php if($product->compare_price): ?>
                                    <span class="text-sm text-gray-500 line-through"><?php echo e($product->formatted_compare_price); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="text-xs text-gray-500">
                                    <?php echo e($product->stock_status); ?>

                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <a href="<?php echo e(route('shop.product', $product->slug)); ?>" class="w-full btn-primary text-center block">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                
                <!-- Pagination -->
                <div class="flex justify-center">
                    <?php echo e($products->links()); ?>

                </div>
                <?php else: ?>
                <!-- No Products Found -->
                <div class="text-center py-12">
                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                    <p class="text-gray-600 mb-4">
                        <?php if(request('search')): ?>
                            No products match your search for "<?php echo e(request('search')); ?>".
                        <?php else: ?>
                            No products match your current filters.
                        <?php endif; ?>
                    </p>
                    <a href="<?php echo e(route('shop.index')); ?>" class="btn-primary">
                        Browse All Products
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php $__env->startPush('scripts'); ?>
<script>
function updateSort(select) {
    const url = new URL(window.location);
    url.searchParams.set('sort', select.value);
    window.location.href = url.toString();
}

function clearCategory() {
    const url = new URL(window.location);
    url.searchParams.delete('category');
    window.location.href = url.toString();
}

// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('filter-form');
    const inputs = form.querySelectorAll('input[type="radio"], input[type="number"]');
    
    inputs.forEach(input => {
        if (input.type === 'radio') {
            input.addEventListener('change', function() {
                form.submit();
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('structured_data'); ?>
<script type="application/ld+json">
{
    "@context": "https://schema.org/",
    "@type": "CollectionPage",
    "name": "Shop - <?php echo e(__('common.company_name')); ?>",
    "description": "Browse our collection of digital products and services. Find the perfect solution for your business needs.",
    "url": "<?php echo e(route('shop.index')); ?>",
    "mainEntity": {
        "@type": "ItemList",
        "numberOfItems": "<?php echo e($products->total()); ?>",
        "itemListElement": [
            <?php $__currentLoopData = $products->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            {
                "@type": "Product",
                "position": <?php echo e($index + 1); ?>,
                "name": "<?php echo e($product->name); ?>",
                "url": "<?php echo e(route('shop.product', $product->slug)); ?>",
                "image": "<?php echo e($product->featured_image ? asset('storage/' . $product->featured_image) : $product->primary_image); ?>",
                <?php if($product->short_description): ?>
                "description": "<?php echo e($product->short_description); ?>",
                <?php endif; ?>
                "offers": {
                    "@type": "Offer",
                    "price": "<?php echo e($product->price); ?>",
                    "priceCurrency": "ZAR",
                    "availability": "<?php echo e($product->isInStock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'); ?>"
                }
            }<?php if(!$loop->last): ?>,<?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        ]
    },
    "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "<?php echo e(route('home')); ?>"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Shop",
                "item": "<?php echo e(route('shop.index')); ?>"
            }
        ]
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/pages/shop/index.blade.php ENDPATH**/ ?>